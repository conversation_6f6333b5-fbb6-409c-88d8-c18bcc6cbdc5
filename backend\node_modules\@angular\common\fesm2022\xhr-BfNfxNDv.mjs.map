{"version": 3, "file": "xhr-BfNfxNDv.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/cookie.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/platform_id.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/xhr.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function parseCookieValue(cookieStr: string, name: string): string | null {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue]: string[] =\n      eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const PLATFORM_BROWSER_ID = 'browser';\nexport const PLATFORM_SERVER_ID = 'server';\n\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nexport function isPlatformBrowser(platformId: Object): boolean {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nexport function isPlatformServer(platformId: Object): boolean {\n  return platformId === PLATFORM_SERVER_ID;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nexport abstract class XhrFactory {\n  abstract build(): XMLHttpRequest;\n}\n"], "names": [], "mappings": ";;;;;;AAQgB,SAAA,gBAAgB,CAAC,SAAiB,EAAE,IAAY,EAAA;AAC9D,IAAA,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC;IAC/B,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACzC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;AACnC,QAAA,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAC7B,OAAO,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;AACtF,QAAA,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AAC9B,YAAA,OAAO,kBAAkB,CAAC,WAAW,CAAC;;;AAG1C,IAAA,OAAO,IAAI;AACb;;ACXO,MAAM,mBAAmB,GAAG;AAC5B,MAAM,kBAAkB,GAAG;AAElC;;;AAGG;AACG,SAAU,iBAAiB,CAAC,UAAkB,EAAA;IAClD,OAAO,UAAU,KAAK,mBAAmB;AAC3C;AAEA;;;AAGG;AACG,SAAU,gBAAgB,CAAC,UAAkB,EAAA;IACjD,OAAO,UAAU,KAAK,kBAAkB;AAC1C;;ACjBA;;;;AAIG;MACmB,UAAU,CAAA;AAE/B;;;;"}