/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
export default [[["gauerdia", "goizald.", "goizeko", "eguerd.", "arrats.", "iluntz.", "gaueko"], u, ["gauerdia", "goizaldeko", "goizeko", "eguerdiko", "arratsaldeko", "iluntzeko", "gaueko"]], [["gauerdia", "goizald.", "goiza", "eguerd.", "arrats.", "iluntz.", "gaua"], ["gauerdia", "goiz.", "goiza", "eguerd.", "arrats.", "iluntz.", "gaua"], ["gauerdia", "goizaldea", "goiza", "eguerdia", "arratsaldea", "iluntzea", "gaua"]], ["00:00", ["00:00", "06:00"], ["06:00", "12:00"], ["12:00", "14:00"], ["14:00", "19:00"], ["19:00", "21:00"], ["21:00", "24:00"]]];
//# sourceMappingURL=data:application/json;base64,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