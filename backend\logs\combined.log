{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 19:49:25"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 19:49:25"}
{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 19:49:55"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 19:55:09"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 19:55:09"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 20:42:04"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 20:42:04"}
{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 20:42:34"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 21:13:42"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 21:13:42"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 21:13:47"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 21:13:47"}
{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 21:14:17"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 21:39:05"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 21:39:05"}
{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 21:39:35"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-20 21:49:50"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-20 21:49:50"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-05-20 21:49:50"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-21 01:24:12"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-21 01:24:12"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-05-21 01:24:12"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-23 19:46:06"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-23 19:46:06"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-05-23 19:46:06"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-23 21:11:42"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-23 21:11:42"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-05-23 21:11:42"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-23 22:29:54"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-23 22:29:54"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-05-23 22:29:54"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-05-24 00:27:27"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-05-24 00:27:27"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-05-24 00:27:27"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-13 02:39:19"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-13 02:39:19"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-13 02:39:19"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-13 02:42:05"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-13 02:42:05"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-13 02:42:05"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-17 20:14:06"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-17 20:14:06"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-17 20:14:06"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-17 21:32:39"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-17 21:32:39"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-17 21:32:39"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:31:26"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:31:26"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:31:26"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:31:39"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:31:39"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:31:39"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:25"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:25"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:25"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:41"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:41"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:41"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:54"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:54"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:32:54"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:33:53"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:33:53"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:33:53"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:34:29"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:34:29"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:34:29"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:35:55"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:35:55"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:35:55"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-21 00:36:29"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-21 00:36:29"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-21 00:36:29"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 16:05:21"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 16:05:21"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 16:05:21"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 16:08:35"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 16:08:35"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 16:08:35"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:49"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:49"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:49"}
{"level":"info","message":"New user created: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:59"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:59"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:18"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:19"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:19"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:25"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:25"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 16:53:41"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:53:41"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:53:41"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 17:06:57"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 17:06:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 17:06:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 17:15:42"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 17:15:42"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 17:16:33"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 17:16:33"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 17:16:33"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 18:01:04"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 18:01:04"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 18:01:04"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:35"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:35"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:38"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:38"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 18:17:12"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 18:17:13"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:17:13"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:41"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:41"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:44"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:44"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:48"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:48"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:09"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:12"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:12"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:16"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:16"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:50"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:50"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:53"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:53"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:57"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:57"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:35"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:35"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:38"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:38"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:42"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:42"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:52"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:52"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:55"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:55"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:08"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:08"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:11"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:11"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:14"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:14"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:13"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:13"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:16"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:16"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:20"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:20"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:45"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:45"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:48"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:48"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:51"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:51"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:00"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:00"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:04"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:04"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:09"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:41"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:41"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:45"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:45"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:06"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:06"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:09"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 19:29:06"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 19:29:06"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 19:29:06"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:52"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:52"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:55"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:55"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:24:47"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:24:47"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:31"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:31"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:34"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:34"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:38"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:38"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:41:54"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:41:54"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:05"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:05"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:09"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:15"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:15"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:15"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:39"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:40"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:40"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:46"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:46"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-22 21:18:47"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-22 21:18:47"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-22 21:18:47"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 22:15:23"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 22:15:24"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 22:15:24"}
{"level":"info","message":"User logged in: captain.sexy (323345923964928001)","service":"hype-hive-backend","timestamp":"2025-06-22 22:47:57"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 22:47:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 22:47:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:48"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:48"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:52"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:52"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-26 22:26:29"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-26 22:26:29"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-26 22:26:29"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-26 23:30:44"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-26 23:30:44"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-26 23:30:44"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-26 23:35:42"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-26 23:35:42"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-26 23:35:42"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-26 23:49:08"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-26 23:49:08"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-26 23:49:08"}
{"level":"info","message":"Server running in development mode on port 4200","service":"hype-hive-backend","timestamp":"2025-06-27 00:15:12"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-27 00:15:12"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-27 00:15:12"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-27 00:15:47"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-27 00:15:47"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-27 00:15:47"}
{"level":"info","message":"Server running in development mode on port 3000","service":"hype-hive-backend","timestamp":"2025-06-27 04:15:51"}
{"level":"info","message":"Socket.io server initialized","service":"hype-hive-backend","timestamp":"2025-06-27 04:15:51"}
{"level":"info","message":"MongoDB Connected: localhost","service":"hype-hive-backend","timestamp":"2025-06-27 04:15:51"}
