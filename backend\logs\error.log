{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 19:49:55"}
{"level":"error","message":"<PERSON>rror connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 20:42:34"}
{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 21:14:17"}
{"level":"error","message":"Error connecting to MongoDB: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"hype-hive-backend","timestamp":"2025-05-20 21:39:35"}
{"level":"error","message":"<PERSON>rror exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:59"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:20:59"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:19"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:19"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:25"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:48:25"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 16:53:41"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 16:53:41"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 17:06:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 17:06:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 17:15:42"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 17:15:42"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 17:16:33"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 17:16:33"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:35"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:35"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:38"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:03:38"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 18:17:13"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:17:13"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:41"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:41"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:44"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:44"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:48"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:20:48"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:09"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:12"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:12"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:16"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:22:16"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:50"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:50"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:53"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:53"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:57"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:23:57"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:35"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:35"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:38"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:38"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:42"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:25:42"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:52"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:52"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:55"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:55"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:27:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:08"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:08"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:11"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:11"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:14"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:29:14"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:13"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:13"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:16"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:16"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:20"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:32:20"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:45"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:45"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:48"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:48"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:51"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:35:51"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:00"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:00"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:04"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:04"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:48:09"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:41"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:41"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:45"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:53:45"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:06"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:06"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 401","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 18:58:09"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:52"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:52"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:55"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:55"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:23:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:24:47"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:24:47"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:31"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:31"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:34"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:34"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:38"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:37:38"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:41:54"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:41:54"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:05"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:05"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:09"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 20:42:09"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:40"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:40"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:46"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 21:17:46"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 22:15:24"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 22:15:24"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 22:47:58"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 22:47:58"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:48"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:48"}
{"level":"error","message":"Error exchanging code for token: Request failed with status code 400","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:52"}
{"level":"error","message":"Discord callback error: Failed to exchange authorization code for token","service":"hype-hive-backend","timestamp":"2025-06-22 23:30:52"}
