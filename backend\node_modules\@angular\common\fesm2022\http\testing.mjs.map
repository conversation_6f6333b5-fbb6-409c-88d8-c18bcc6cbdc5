{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/testing/src/api.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/testing/src/request.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/testing/src/backend.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/testing/src/provider.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/testing/src/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpRequest} from '../../index';\n\nimport {TestRequest} from './request';\n\n/**\n * Defines a matcher for requests based on URL, method, or both.\n *\n * @publicApi\n */\nexport interface RequestMatch {\n  method?: string;\n  url?: string;\n}\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nexport abstract class HttpTestingController {\n  /**\n   * Search for requests that match the given parameter, without any expectations.\n   */\n  abstract match(\n    match: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n  ): TestRequest[];\n\n  /**\n   * Expect that a single request has been made which matches the given URL, and return its\n   * mock.\n   *\n   * If no such request has been made, or more than one such request has been made, fail with an\n   * error message including the given request description, if any.\n   */\n  abstract expectOne(url: string, description?: string): TestRequest;\n\n  /**\n   * Expect that a single request has been made which matches the given parameters, and return\n   * its mock.\n   *\n   * If no such request has been made, or more than one such request has been made, fail with an\n   * error message including the given request description, if any.\n   */\n  abstract expectOne(params: RequestMatch, description?: string): TestRequest;\n\n  /**\n   * Expect that a single request has been made which matches the given predicate function, and\n   * return its mock.\n   *\n   * If no such request has been made, or more than one such request has been made, fail with an\n   * error message including the given request description, if any.\n   */\n  abstract expectOne(\n    matchFn: (req: HttpRequest<any>) => boolean,\n    description?: string,\n  ): TestRequest;\n\n  /**\n   * Expect that a single request has been made which matches the given condition, and return\n   * its mock.\n   *\n   * If no such request has been made, or more than one such request has been made, fail with an\n   * error message including the given request description, if any.\n   */\n  abstract expectOne(\n    match: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n    description?: string,\n  ): TestRequest;\n\n  /**\n   * Expect that no requests have been made which match the given URL.\n   *\n   * If a matching request has been made, fail with an error message including the given request\n   * description, if any.\n   */\n  abstract expectNone(url: string, description?: string): void;\n\n  /**\n   * Expect that no requests have been made which match the given parameters.\n   *\n   * If a matching request has been made, fail with an error message including the given request\n   * description, if any.\n   */\n  abstract expectNone(params: RequestMatch, description?: string): void;\n\n  /**\n   * Expect that no requests have been made which match the given predicate function.\n   *\n   * If a matching request has been made, fail with an error message including the given request\n   * description, if any.\n   */\n  abstract expectNone(matchFn: (req: HttpRequest<any>) => boolean, description?: string): void;\n\n  /**\n   * Expect that no requests have been made which match the given condition.\n   *\n   * If a matching request has been made, fail with an error message including the given request\n   * description, if any.\n   */\n  abstract expectNone(\n    match: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n    description?: string,\n  ): void;\n\n  /**\n   * Verify that no unmatched requests are outstanding.\n   *\n   * If any requests are outstanding, fail with an error message indicating which requests were not\n   * handled.\n   *\n   * If `ignoreCancelled` is not set (the default), `verify()` will also fail if cancelled requests\n   * were not explicitly matched.\n   */\n  abstract verify(opts?: {ignoreCancelled?: boolean}): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  HttpErrorResponse,\n  HttpEvent,\n  HttpHeaders,\n  HttpRequest,\n  HttpResponse,\n  HttpStatusCode,\n} from '../../index';\nimport {Observer} from 'rxjs';\n\n/**\n * Type that describes options that can be used to create an error\n * in `TestRequest`.\n */\ntype TestRequestErrorOptions = {\n  headers?: HttpHeaders | {[name: string]: string | string[]};\n  status?: number;\n  statusText?: string;\n};\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nexport class TestRequest {\n  /**\n   * Whether the request was cancelled after it was sent.\n   */\n  get cancelled(): boolean {\n    return this._cancelled;\n  }\n\n  /**\n   * @internal set by `HttpClientTestingBackend`\n   */\n  _cancelled = false;\n\n  constructor(\n    public request: HttpRequest<any>,\n    private observer: Observer<HttpEvent<any>>,\n  ) {}\n\n  /**\n   * Resolve the request by returning a body plus additional HTTP information (such as response\n   * headers) if provided.\n   * If the request specifies an expected body type, the body is converted into the requested type.\n   * Otherwise, the body is converted to `JSON` by default.\n   *\n   * Both successful and unsuccessful responses can be delivered via `flush()`.\n   */\n  flush(\n    body:\n      | ArrayBuffer\n      | Blob\n      | boolean\n      | string\n      | number\n      | Object\n      | (boolean | string | number | Object | null)[]\n      | null,\n    opts: {\n      headers?: HttpHeaders | {[name: string]: string | string[]};\n      status?: number;\n      statusText?: string;\n    } = {},\n  ): void {\n    if (this.cancelled) {\n      throw new Error(`Cannot flush a cancelled request.`);\n    }\n    const url = this.request.urlWithParams;\n    const headers =\n      opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    body = _maybeConvertBody(this.request.responseType, body);\n    let statusText: string | undefined = opts.statusText;\n    let status: number = opts.status !== undefined ? opts.status : HttpStatusCode.Ok;\n    if (opts.status === undefined) {\n      if (body === null) {\n        status = HttpStatusCode.NoContent;\n        statusText ||= 'No Content';\n      } else {\n        statusText ||= 'OK';\n      }\n    }\n    if (statusText === undefined) {\n      throw new Error('statusText is required when setting a custom status.');\n    }\n    if (status >= 200 && status < 300) {\n      this.observer.next(new HttpResponse<any>({body, headers, status, statusText, url}));\n      this.observer.complete();\n    } else {\n      this.observer.error(new HttpErrorResponse({error: body, headers, status, statusText, url}));\n    }\n  }\n\n  /**\n   * Resolve the request by returning an `ErrorEvent` (e.g. simulating a network failure).\n   * @deprecated Http requests never emit an `ErrorEvent`. Please specify a `ProgressEvent`.\n   */\n  error(error: ErrorEvent, opts?: TestRequestErrorOptions): void;\n  /**\n   * Resolve the request by returning an `ProgressEvent` (e.g. simulating a network failure).\n   */\n  error(error: ProgressEvent, opts?: TestRequestErrorOptions): void;\n  error(error: ProgressEvent | ErrorEvent, opts: TestRequestErrorOptions = {}): void {\n    if (this.cancelled) {\n      throw new Error(`Cannot return an error for a cancelled request.`);\n    }\n    if (opts.status && opts.status >= 200 && opts.status < 300) {\n      throw new Error(`error() called with a successful status.`);\n    }\n    const headers =\n      opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    this.observer.error(\n      new HttpErrorResponse({\n        error,\n        headers,\n        status: opts.status || 0,\n        statusText: opts.statusText || '',\n        url: this.request.urlWithParams,\n      }),\n    );\n  }\n\n  /**\n   * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n   * request.\n   */\n  event(event: HttpEvent<any>): void {\n    if (this.cancelled) {\n      throw new Error(`Cannot send events to a cancelled request.`);\n    }\n    this.observer.next(event);\n  }\n}\n\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(\n  body: ArrayBuffer | Blob | string | number | Object | (string | number | Object | null)[],\n): ArrayBuffer {\n  if (typeof ArrayBuffer === 'undefined') {\n    throw new Error('ArrayBuffer responses are not supported on this platform.');\n  }\n  if (body instanceof ArrayBuffer) {\n    return body;\n  }\n  throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(\n  body: ArrayBuffer | Blob | string | number | Object | (string | number | Object | null)[],\n): Blob {\n  if (typeof Blob === 'undefined') {\n    throw new Error('Blob responses are not supported on this platform.');\n  }\n  if (body instanceof Blob) {\n    return body;\n  }\n  if (ArrayBuffer && body instanceof ArrayBuffer) {\n    return new Blob([body]);\n  }\n  throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(\n  body:\n    | ArrayBuffer\n    | Blob\n    | boolean\n    | string\n    | number\n    | Object\n    | (boolean | string | number | Object | null)[],\n  format: string = 'JSON',\n): Object | string | number | (Object | string | number)[] {\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n  }\n  if (\n    typeof body === 'string' ||\n    typeof body === 'number' ||\n    typeof body === 'object' ||\n    typeof body === 'boolean' ||\n    Array.isArray(body)\n  ) {\n    return body;\n  }\n  throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(\n  body: ArrayBuffer | Blob | string | number | Object | (string | number | Object | null)[],\n): string {\n  if (typeof body === 'string') {\n    return body;\n  }\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error('Automatic conversion to text is not supported for Blobs.');\n  }\n  return JSON.stringify(_toJsonBody(body, 'text'));\n}\n\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(\n  responseType: string,\n  body: ArrayBuffer | Blob | string | number | Object | (string | number | Object | null)[] | null,\n): ArrayBuffer | Blob | string | number | Object | (string | number | Object | null)[] | null {\n  if (body === null) {\n    return null;\n  }\n  switch (responseType) {\n    case 'arraybuffer':\n      return _toArrayBufferBody(body);\n    case 'blob':\n      return _toBlob(body);\n    case 'json':\n      return _toJsonBody(body);\n    case 'text':\n      return _toTextBody(body);\n    default:\n      throw new Error(`Unsupported responseType: ${responseType}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpBackend, HttpEvent, HttpEventType, HttpRequest} from '../../index';\nimport {Injectable} from '@angular/core';\nimport {Observable, Observer} from 'rxjs';\n\nimport {HttpTestingController, RequestMatch} from './api';\nimport {TestRequest} from './request';\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\n@Injectable()\nexport class HttpClientTestingBackend implements HttpBackend, HttpTestingController {\n  /**\n   * List of pending requests which have not yet been expected.\n   */\n  private open: TestRequest[] = [];\n\n  /**\n   * Used when checking if we need to throw the NOT_USING_FETCH_BACKEND_IN_SSR error\n   */\n  private isTestingBackend = true;\n\n  /**\n   * Handle an incoming request by queueing it in the list of open requests.\n   */\n  handle(req: HttpRequest<any>): Observable<HttpEvent<any>> {\n    return new Observable((observer: Observer<any>) => {\n      const testReq = new TestRequest(req, observer);\n      this.open.push(testReq);\n      observer.next({type: HttpEventType.Sent} as HttpEvent<any>);\n      return () => {\n        testReq._cancelled = true;\n      };\n    });\n  }\n\n  /**\n   * Helper function to search for requests in the list of open requests.\n   */\n  private _match(\n    match: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n  ): TestRequest[] {\n    if (typeof match === 'string') {\n      return this.open.filter((testReq) => testReq.request.urlWithParams === match);\n    } else if (typeof match === 'function') {\n      return this.open.filter((testReq) => match(testReq.request));\n    } else {\n      return this.open.filter(\n        (testReq) =>\n          (!match.method || testReq.request.method === match.method.toUpperCase()) &&\n          (!match.url || testReq.request.urlWithParams === match.url),\n      );\n    }\n  }\n\n  /**\n   * Search for requests in the list of open requests, and return all that match\n   * without asserting anything about the number of matches.\n   */\n  match(match: string | RequestMatch | ((req: HttpRequest<any>) => boolean)): TestRequest[] {\n    const results = this._match(match);\n    results.forEach((result) => {\n      const index = this.open.indexOf(result);\n      if (index !== -1) {\n        this.open.splice(index, 1);\n      }\n    });\n    return results;\n  }\n\n  /**\n   * Expect that a single outstanding request matches the given matcher, and return\n   * it.\n   *\n   * Requests returned through this API will no longer be in the list of open requests,\n   * and thus will not match twice.\n   */\n  expectOne(\n    match: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n    description?: string,\n  ): TestRequest {\n    description ||= this.descriptionFromMatcher(match);\n    const matches = this.match(match);\n    if (matches.length > 1) {\n      throw new Error(\n        `Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`,\n      );\n    }\n    if (matches.length === 0) {\n      let message = `Expected one matching request for criteria \"${description}\", found none.`;\n      if (this.open.length > 0) {\n        // Show the methods and URLs of open requests in the error, for convenience.\n        const requests = this.open.map(describeRequest).join(', ');\n        message += ` Requests received are: ${requests}.`;\n      }\n      throw new Error(message);\n    }\n    return matches[0];\n  }\n\n  /**\n   * Expect that no outstanding requests match the given matcher, and throw an error\n   * if any do.\n   */\n  expectNone(\n    match: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n    description?: string,\n  ): void {\n    description ||= this.descriptionFromMatcher(match);\n    const matches = this.match(match);\n    if (matches.length > 0) {\n      throw new Error(\n        `Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`,\n      );\n    }\n  }\n\n  /**\n   * Validate that there are no outstanding requests.\n   */\n  verify(opts: {ignoreCancelled?: boolean} = {}): void {\n    let open = this.open;\n    // It's possible that some requests may be cancelled, and this is expected.\n    // The user can ask to ignore open requests which have been cancelled.\n    if (opts.ignoreCancelled) {\n      open = open.filter((testReq) => !testReq.cancelled);\n    }\n    if (open.length > 0) {\n      // Show the methods and URLs of open requests in the error, for convenience.\n      const requests = open.map(describeRequest).join(', ');\n      throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n    }\n  }\n\n  private descriptionFromMatcher(\n    matcher: string | RequestMatch | ((req: HttpRequest<any>) => boolean),\n  ): string {\n    if (typeof matcher === 'string') {\n      return `Match URL: ${matcher}`;\n    } else if (typeof matcher === 'object') {\n      const method = matcher.method || '(any)';\n      const url = matcher.url || '(any)';\n      return `Match method: ${method}, URL: ${url}`;\n    } else {\n      return `Match by function: ${matcher.name}`;\n    }\n  }\n}\n\nfunction describeRequest(testRequest: TestRequest): string {\n  const url = testRequest.request.urlWithParams;\n  const method = testRequest.request.method;\n  return `${method} ${url}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpBackend, ɵREQUESTS_CONTRIBUTE_TO_STABILITY} from '../../index';\nimport {Provider} from '@angular/core';\n\nimport {HttpTestingController} from './api';\nimport {HttpClientTestingBackend} from './backend';\n\nexport function provideHttpClientTesting(): Provider[] {\n  return [\n    HttpClientTestingBackend,\n    {provide: HttpBackend, useExisting: HttpClientTestingBackend},\n    {provide: HttpTestingController, useExisting: HttpClientTestingBackend},\n    {provide: ɵREQUESTS_CONTRIBUTE_TO_STABILITY, useValue: false},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpClientModule} from '../../index';\nimport {NgModule} from '@angular/core';\n\nimport {provideHttpClientTesting} from './provider';\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n *\n * @deprecated Add `provideHttpClientTesting()` to your providers instead.\n */\n@NgModule({\n  imports: [HttpClientModule],\n  providers: [provideHttpClientTesting()],\n})\nexport class HttpClientTestingModule {}\n"], "names": ["ɵREQUESTS_CONTRIBUTE_TO_STABILITY"], "mappings": ";;;;;;;;;;;;;;AAsBA;;;;;AAKG;MACmB,qBAAqB,CAAA;AA+F1C;;AC/FD;;;;;;;AAOG;MACU,WAAW,CAAA;AAcb,IAAA,OAAA;AACC,IAAA,QAAA;AAdV;;AAEG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;AAGxB;;AAEG;IACH,UAAU,GAAG,KAAK;IAElB,WACS,CAAA,OAAyB,EACxB,QAAkC,EAAA;QADnC,IAAO,CAAA,OAAA,GAAP,OAAO;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAGlB;;;;;;;AAOG;AACH,IAAA,KAAK,CACH,IAQQ,EACR,IAAA,GAII,EAAE,EAAA;AAEN,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,iCAAA,CAAmC,CAAC;;AAEtD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;QACtC,MAAM,OAAO,GACX,IAAI,CAAC,OAAO,YAAY,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;QACpF,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;AACzD,QAAA,IAAI,UAAU,GAAuB,IAAI,CAAC,UAAU;AACpD,QAAA,IAAI,MAAM,GAAW,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,EAAE;AAChF,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;AAC7B,YAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,gBAAA,MAAM,GAAG,cAAc,CAAC,SAAS;gBACjC,UAAU,KAAK,YAAY;;iBACtB;gBACL,UAAU,KAAK,IAAI;;;AAGvB,QAAA,IAAI,UAAU,KAAK,SAAS,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC;;QAEzE,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAM,EAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAC,CAAC,CAAC;AACnF,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;aACnB;YACL,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAC,CAAC,CAAC;;;AAa/F,IAAA,KAAK,CAAC,KAAiC,EAAE,IAAA,GAAgC,EAAE,EAAA;AACzE,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,+CAAA,CAAiD,CAAC;;AAEpE,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;AAC1D,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,wCAAA,CAA0C,CAAC;;QAE7D,MAAM,OAAO,GACX,IAAI,CAAC,OAAO,YAAY,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AACpF,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CACjB,IAAI,iBAAiB,CAAC;YACpB,KAAK;YACL,OAAO;AACP,YAAA,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;AACxB,YAAA,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE;AACjC,YAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;AAChC,SAAA,CAAC,CACH;;AAGH;;;AAGG;AACH,IAAA,KAAK,CAAC,KAAqB,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,0CAAA,CAA4C,CAAC;;AAE/D,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE5B;AAED;;AAEG;AACH,SAAS,kBAAkB,CACzB,IAAyF,EAAA;AAEzF,IAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACtC,QAAA,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC;;AAE9E,IAAA,IAAI,IAAI,YAAY,WAAW,EAAE;AAC/B,QAAA,OAAO,IAAI;;AAEb,IAAA,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC;AAC5F;AAEA;;AAEG;AACH,SAAS,OAAO,CACd,IAAyF,EAAA;AAEzF,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC/B,QAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC;;AAEvE,IAAA,IAAI,IAAI,YAAY,IAAI,EAAE;AACxB,QAAA,OAAO,IAAI;;AAEb,IAAA,IAAI,WAAW,IAAI,IAAI,YAAY,WAAW,EAAE;AAC9C,QAAA,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;;AAEzB,IAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC;AACrF;AAEA;;AAEG;AACH,SAAS,WAAW,CAClB,IAOiD,EACjD,SAAiB,MAAM,EAAA;IAEvB,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,IAAI,YAAY,WAAW,EAAE;AACrE,QAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAA,mCAAA,CAAqC,CAAC;;IAEzF,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;AACvD,QAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAA,4BAAA,CAA8B,CAAC;;IAElF,IACE,OAAO,IAAI,KAAK,QAAQ;QACxB,OAAO,IAAI,KAAK,QAAQ;QACxB,OAAO,IAAI,KAAK,QAAQ;QACxB,OAAO,IAAI,KAAK,SAAS;AACzB,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EACnB;AACA,QAAA,OAAO,IAAI;;AAEb,IAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAA,oCAAA,CAAsC,CAAC;AAC1F;AAEA;;AAEG;AACH,SAAS,WAAW,CAClB,IAAyF,EAAA;AAEzF,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,QAAA,OAAO,IAAI;;IAEb,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,IAAI,YAAY,WAAW,EAAE;AACrE,QAAA,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC;;IAEpF,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;AACvD,QAAA,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC;;IAE7E,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAClD;AAEA;;AAEG;AACH,SAAS,iBAAiB,CACxB,YAAoB,EACpB,IAAgG,EAAA;AAEhG,IAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,QAAA,OAAO,IAAI;;IAEb,QAAQ,YAAY;AAClB,QAAA,KAAK,aAAa;AAChB,YAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC;AACjC,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,OAAO,CAAC,IAAI,CAAC;AACtB,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,WAAW,CAAC,IAAI,CAAC;AAC1B,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,WAAW,CAAC,IAAI,CAAC;AAC1B,QAAA;AACE,YAAA,MAAM,IAAI,KAAK,CAAC,6BAA6B,YAAY,CAAA,CAAE,CAAC;;AAElE;;AC7OA;;;;;;;;;;AAUG;MAEU,wBAAwB,CAAA;AACnC;;AAEG;IACK,IAAI,GAAkB,EAAE;AAEhC;;AAEG;IACK,gBAAgB,GAAG,IAAI;AAE/B;;AAEG;AACH,IAAA,MAAM,CAAC,GAAqB,EAAA;AAC1B,QAAA,OAAO,IAAI,UAAU,CAAC,CAAC,QAAuB,KAAI;YAChD,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC;AAC9C,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAmB,CAAC;AAC3D,YAAA,OAAO,MAAK;AACV,gBAAA,OAAO,CAAC,UAAU,GAAG,IAAI;AAC3B,aAAC;AACH,SAAC,CAAC;;AAGJ;;AAEG;AACK,IAAA,MAAM,CACZ,KAAmE,EAAA;AAEnE,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC;;AACxE,aAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;AACtC,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;aACvD;AACL,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CACrB,CAAC,OAAO,KACN,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE;AACvE,iBAAC,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAC9D;;;AAIL;;;AAGG;AACH,IAAA,KAAK,CAAC,KAAmE,EAAA;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AAClC,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;YACzB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,YAAA,IAAI,KAAK,KAAK,EAAE,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;AAE9B,SAAC,CAAC;AACF,QAAA,OAAO,OAAO;;AAGhB;;;;;;AAMG;IACH,SAAS,CACP,KAAmE,EACnE,WAAoB,EAAA;AAEpB,QAAA,WAAW,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,CAA+C,4CAAA,EAAA,WAAW,CAAY,SAAA,EAAA,OAAO,CAAC,MAAM,CAAY,UAAA,CAAA,CACjG;;AAEH,QAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,YAAA,IAAI,OAAO,GAAG,CAA+C,4CAAA,EAAA,WAAW,gBAAgB;YACxF,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;;AAExB,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1D,gBAAA,OAAO,IAAI,CAAA,wBAAA,EAA2B,QAAQ,CAAA,CAAA,CAAG;;AAEnD,YAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC;;AAE1B,QAAA,OAAO,OAAO,CAAC,CAAC,CAAC;;AAGnB;;;AAGG;IACH,UAAU,CACR,KAAmE,EACnE,WAAoB,EAAA;AAEpB,QAAA,WAAW,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,CAAiD,8CAAA,EAAA,WAAW,CAAY,SAAA,EAAA,OAAO,CAAC,MAAM,CAAG,CAAA,CAAA,CAC1F;;;AAIL;;AAEG;IACH,MAAM,CAAC,OAAoC,EAAE,EAAA;AAC3C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;;;AAGpB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;;AAErD,QAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEnB,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,CAAoC,iCAAA,EAAA,IAAI,CAAC,MAAM,CAAK,EAAA,EAAA,QAAQ,CAAE,CAAA,CAAC;;;AAI3E,IAAA,sBAAsB,CAC5B,OAAqE,EAAA;AAErE,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,OAAO,CAAA,WAAA,EAAc,OAAO,CAAA,CAAE;;AACzB,aAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACtC,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO;AACxC,YAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO;AAClC,YAAA,OAAO,CAAiB,cAAA,EAAA,MAAM,CAAU,OAAA,EAAA,GAAG,EAAE;;aACxC;AACL,YAAA,OAAO,CAAsB,mBAAA,EAAA,OAAO,CAAC,IAAI,EAAE;;;kHArIpC,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAxB,wBAAwB,EAAA,CAAA;;sGAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC;;AA2ID,SAAS,eAAe,CAAC,WAAwB,EAAA;AAC/C,IAAA,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa;AAC7C,IAAA,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM;AACzC,IAAA,OAAO,CAAG,EAAA,MAAM,CAAI,CAAA,EAAA,GAAG,EAAE;AAC3B;;SC3JgB,wBAAwB,GAAA;IACtC,OAAO;QACL,wBAAwB;AACxB,QAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,wBAAwB,EAAC;AAC7D,QAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,wBAAwB,EAAC;AACvE,QAAA,EAAC,OAAO,EAAEA,gCAAiC,EAAE,QAAQ,EAAE,KAAK,EAAC;KAC9D;AACH;;ACRA;;;;;;;;AAQG;MAKU,uBAAuB,CAAA;kHAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,YAHxB,gBAAgB,CAAA,EAAA,CAAA;AAGf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,aAFvB,CAAC,wBAAwB,EAAE,CAAC,YAD7B,gBAAgB,CAAA,EAAA,CAAA;;sGAGf,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,gBAAgB,CAAC;AAC3B,oBAAA,SAAS,EAAE,CAAC,wBAAwB,EAAE,CAAC;AACxC,iBAAA;;;;;"}